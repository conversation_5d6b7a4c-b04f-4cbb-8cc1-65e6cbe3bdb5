# Audit Trail

## Tuesday, June 24, 2025

### Grid Support Sensors - Added Write Capabilities

**Objective**: Transform the two Grid Support sensors from read-only to read-write capable while maintaining their current functionality.

**Changes Made**:
1. **Preserved existing read-only sensors**: Kept the original `Inv1_Grid_Support` and `Inv2_Grid_Support` sensors intact to maintain backward compatibility and existing read functionality.

2. **Added new switch entities for write capabilities**:
   - `Inv1_Grid_Support_Control` (slave: 10, address: 435)
   - `Inv2_Grid_Support_Control` (slave: 12, address: 435)

**Technical Implementation**:
- **Entity Type**: Added new `switches` section to the Modbus configuration
- **Write Type**: Used `holding` register type for write operations
- **Commands**: Configured `command_on: 1` and `command_off: 0` for binary control
- **Verification**: Added `verify` configuration to read back the written values for confirmation
- **Scan Interval**: Maintained 20-second scan interval consistent with original sensors
- **Address**: Used same address (435) as the original sensors for both read and write operations

**Configuration Details**:
- **write_type: holding**: Enables writing to holding registers using the `write_register` function
- **verify section**: Ensures written values are confirmed by reading back from the same register
- **state_on/state_off**: Defines expected values (1/0) when reading back the register state

**Benefits**:
- Maintains existing read-only functionality for backward compatibility
- Adds new write-capable entities for control operations
- Follows Home Assistant Modbus integration best practices
- Includes verification to ensure write operations are successful
- Uses proper YAML formatting and indentation

**Outcome**: Successfully added write capabilities while preserving existing read functionality. The configuration now supports both monitoring (via sensors) and control (via switches) of the Grid Support functionality on both inverters.
