# Loads default set of integrations. Do not remove.
default_config:

# Load frontend themes from the themes folder
frontend:
  themes: !include_dir_merge_named themes

# Text to speech
tts:
  - platform: google_translate

automation: !include automations.yaml
script: !include scripts.yaml
scene: !include scenes.yaml

modbus:
  - name: Modbus_Hub
    type: tcp
    host: *************
    port: 502
    delay: 2
    message_wait_milliseconds: 0
    timeout: 5
    # Existing sensors for reading data
    sensors:
      - name: MPPT_DC_Power
        unique_id: MPPT_DC_Power
        slave: 30
        address: 92
        unit_of_measurement: Wh
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
      - name: MPPT_DC_Voltage
        unique_id: MPPT_DC_Voltage
        slave: 30
        address: 0x0058
        unit_of_measurement: V
        input_type: holding
        data_type: int16
        scale: 0.001
        offset: 0.0
        precision: 2
      - name: Home_Load
        unique_id: Home_Load
        slave: 201
        address: 94
        unit_of_measurement: Wh
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
      - name: Grid_AC_Power
        unique_id: Grid_AC_Power
        slave: 201
        address: 82
        unit_of_measurement: Wh
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
        device_class: energy
        state_class: total
      - name: Inv1_Battery_Power
        unique_id: Inv1_Battery_Power
        slave: 10
        address: 0x0054
        unit_of_measurement: Wh
        input_type: holding
        data_type: int16
        scale: 1.0
        offset: 0.0
      - name: Inv2_Battery_Power
        unique_id: Inv2_Battery_Power
        slave: 12
        address: 0x0054
        unit_of_measurement: Wh
        input_type: holding
        data_type: int16
        scale: 1.0
        offset: 0.0
      - name: Battery_Temperature
        unique_id: Battery_Temperature
        slave: 10
        address: 86
        unit_of_measurement: °C
        input_type: holding
        data_type: uint16
        scale: 0.01
        offset: -273.0
      - name: Inv1_Grid_Support
        unique_id: Inv1_Grid_Support
        slave: 10
        slave_count: 0
        scan_interval: 20
        device_class: enum
        address: 435
        data_type: uint16
      - name: Inv2_Grid_Support
        unique_id: Inv2_Grid_Support
        slave: 12
        slave_count: 0
        scan_interval: 20
        device_class: enum
        address: 435
        data_type: uint16
    # Switches for Grid Support with read/write capabilities
    switches:
      - name: Inv1_Grid_Support_Control
        unique_id: Inv1_Grid_Support_Control
        slave: 10
        address: 435
        write_type: holding
        command_on: 1
        command_off: 0
        scan_interval: 20
        verify:
          input_type: holding
          address: 435
          state_on: 1
          state_off: 0
      - name: Inv2_Grid_Support_Control
        unique_id: Inv2_Grid_Support_Control
        slave: 12
        address: 435
        write_type: holding
        command_on: 1
        command_off: 0
        scan_interval: 20
        verify:
          input_type: holding
          address: 435
          state_on: 1
          state_off: 0
